import React, { useState } from 'react';
import { createAllRealEstateForms, verifyRealEstateForms, deleteAllRealEstateForms, REAL_ESTATE_STRUCTURE } from '../../scripts/createRealEstateForms';
import './RealEstateFormsCreator.css';

const RealEstateFormsCreator = () => {
  const [output, setOutput] = useState('');
  const [status, setStatus] = useState({ message: '', type: '' });
  const [isLoading, setIsLoading] = useState(false);

  const addToOutput = (text) => {
    setOutput(prev => prev + text + '\n');
  };

  const clearOutput = () => {
    setOutput('');
  };

  const showStatus = (message, type) => {
    setStatus({ message, type });
  };

  const handleCreateForms = async () => {
    setIsLoading(true);
    clearOutput();
    showStatus('Creating forms...', 'info');
    
    try {
      // Redirect console.log to our output
      const originalLog = console.log;
      const originalError = console.error;
      
      console.log = (message) => {
        addToOutput(message);
        originalLog(message);
      };
      
      console.error = (message, error) => {
        addToOutput(`ERROR: ${message} ${error ? error.toString() : ''}`);
        originalError(message, error);
      };
      
      const result = createAllRealEstateForms();
      
      // Restore console
      console.log = originalLog;
      console.error = originalError;
      
      showStatus(`Successfully created ${result.successful} forms!`, 'success');
      
    } catch (error) {
      console.error('Error creating forms:', error);
      showStatus('Error creating forms: ' + error.message, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyForms = async () => {
    setIsLoading(true);
    clearOutput();
    showStatus('Verifying forms...', 'info');
    
    try {
      const originalLog = console.log;
      
      console.log = (message) => {
        addToOutput(message);
        originalLog(message);
      };
      
      const result = verifyRealEstateForms();
      
      console.log = originalLog;
      
      showStatus(`Verification complete! Found ${result.totalFound} forms.`, 'success');
      
    } catch (error) {
      console.error('Error verifying forms:', error);
      showStatus('Error verifying forms: ' + error.message, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteForms = async () => {
    if (!window.confirm('Are you sure you want to delete all Real Estate forms? This action cannot be undone.')) {
      return;
    }
    
    setIsLoading(true);
    clearOutput();
    showStatus('Deleting forms...', 'info');
    
    try {
      const originalLog = console.log;
      const originalError = console.error;
      
      console.log = (message) => {
        addToOutput(message);
        originalLog(message);
      };
      
      console.error = (message, error) => {
        addToOutput(`ERROR: ${message} ${error ? error.toString() : ''}`);
        originalError(message, error);
      };
      
      const deletedCount = deleteAllRealEstateForms();
      
      console.log = originalLog;
      console.error = originalError;
      
      showStatus(`Successfully deleted ${deletedCount} forms!`, 'success');
      
    } catch (error) {
      console.error('Error deleting forms:', error);
      showStatus('Error deleting forms: ' + error.message, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="real-estate-forms-creator">
      <div className="header">
        <h1>🏢 Real Estate Forms Creator</h1>
        <p>Create comprehensive forms for all Real Estate division subcategories</p>
      </div>

      <div className="structure-display">
        <h3>Real Estate Division Structure:</h3>
        {REAL_ESTATE_STRUCTURE.categories.map(category => (
          <div key={category.id} className="category-item">
            <div className="category-name">
              <strong>{category.name}</strong>
            </div>
            <div className="subcategories">
              {category.subcategories.length > 0 ? (
                category.subcategories.map(subcategory => (
                  <div key={subcategory.id} className="subcategory-item">
                    • {subcategory.name}
                  </div>
                ))
              ) : (
                <div className="subcategory-item">
                  • No sub-categories (category form only)
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="actions">
        <button 
          className="btn btn-success" 
          onClick={handleCreateForms}
          disabled={isLoading}
        >
          {isLoading ? '⏳ Creating...' : '🚀 Create All Forms'}
        </button>
        
        <button 
          className="btn btn-primary" 
          onClick={handleVerifyForms}
          disabled={isLoading}
        >
          {isLoading ? '⏳ Verifying...' : '🔍 Verify Forms'}
        </button>
        
        <button 
          className="btn btn-danger" 
          onClick={handleDeleteForms}
          disabled={isLoading}
        >
          {isLoading ? '⏳ Deleting...' : '🗑️ Delete All Forms'}
        </button>
      </div>

      {status.message && (
        <div className={`status-message ${status.type}`}>
          {status.message}
        </div>
      )}

      {output && (
        <div className="output-container">
          <h4>Output:</h4>
          <pre className="output">{output}</pre>
        </div>
      )}
    </div>
  );
};

export default RealEstateFormsCreator;
