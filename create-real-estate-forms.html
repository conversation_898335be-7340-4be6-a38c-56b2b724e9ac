<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Real Estate Forms</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.success {
            background-color: #28a745;
        }
        .button.success:hover {
            background-color: #218838;
        }
        .structure {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .category {
            margin: 15px 0;
            padding: 15px;
            background-color: white;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .subcategory {
            margin-left: 20px;
            color: #666;
            margin: 5px 0;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 Real Estate Forms Creator</h1>
        
        <div class="instructions">
            <strong>Instructions:</strong>
            <ol>
                <li>This tool will create comprehensive forms for all Real Estate division subcategories</li>
                <li>Each form will include all available person fields (45+ fields)</li>
                <li>Forms will be saved to browser localStorage and can be accessed through the main application</li>
                <li>Click "Create All Forms" to generate forms for all subcategories listed below</li>
            </ol>
        </div>
        
        <div class="structure">
            <h3>Real Estate Division Structure (17 forms will be created):</h3>
            <div class="category">
                <strong>Normal Agents</strong> (4 subcategory forms)
                <div class="subcategory">• Proprietorship</div>
                <div class="subcategory">• Company</div>
                <div class="subcategory">• Individual</div>
                <div class="subcategory">• Partnership</div>
            </div>
            <div class="category">
                <strong>Rera.Reg. Agents</strong> (4 subcategory forms)
                <div class="subcategory">• proprietorship</div>
                <div class="subcategory">• Company</div>
                <div class="subcategory">• Individual</div>
                <div class="subcategory">• Partnership</div>
            </div>
            <div class="category">
                <strong>Rera.Reg. Promoters</strong> (4 subcategory forms)
                <div class="subcategory">• proprietorship</div>
                <div class="subcategory">• Company</div>
                <div class="subcategory">• Individual</div>
                <div class="subcategory">• Partnership</div>
            </div>
            <div class="category">
                <strong>Colonisers</strong> (4 subcategory forms)
                <div class="subcategory">• Partnership</div>
                <div class="subcategory">• proprietorship</div>
                <div class="subcategory">• Company</div>
                <div class="subcategory">• Individual</div>
            </div>
            <div class="category">
                <strong>Others</strong> (1 category form)
                <div class="subcategory">• No sub-categories (category form only)</div>
            </div>
        </div>

        <div style="text-align: center;">
            <button class="button success" onclick="createForms()">
                🚀 Create All Forms (17 forms)
            </button>
        </div>

        <div id="status"></div>
        <div id="output" class="output" style="display: none;"></div>
    </div>

    <script>
        // Real Estate division structure
        const REAL_ESTATE_STRUCTURE = {
            divisionId: 1,
            divisionName: 'Real Estate',
            categories: [
                {
                    id: 1,
                    name: 'Normal Agents',
                    subcategories: [
                        { id: 1, name: 'Proprietorship' },
                        { id: 2, name: 'Company' },
                        { id: 3, name: 'Individual' },
                        { id: 4, name: 'Partnership' }
                    ]
                },
                {
                    id: 2,
                    name: 'Rera.Reg. Agents',
                    subcategories: [
                        { id: 5, name: 'proprietorship' },
                        { id: 6, name: 'Company' },
                        { id: 7, name: 'Individual' },
                        { id: 8, name: 'Partnership' }
                    ]
                },
                {
                    id: 3,
                    name: 'Rera.Reg. Promoters',
                    subcategories: [
                        { id: 9, name: 'proprietorship' },
                        { id: 10, name: 'Company' },
                        { id: 11, name: 'Individual' },
                        { id: 12, name: 'Partnership' }
                    ]
                },
                {
                    id: 4,
                    name: 'Colonisers',
                    subcategories: [
                        { id: 13, name: 'Partnership' },
                        { id: 14, name: 'proprietorship' },
                        { id: 15, name: 'Company' },
                        { id: 16, name: 'Individual' }
                    ]
                },
                {
                    id: 5,
                    name: 'Others',
                    subcategories: []
                }
            ]
        };

        // Simplified person fields (representing the actual 45+ fields)
        const ALL_PERSON_FIELDS = [
            { key: 'name', label: 'Full Name', type: 'text', required: true, section: 'personalInfo' },
            { key: 'mobileNumber', label: 'Mobile Number', type: 'tel', required: true, section: 'contactInfo' },
            { key: 'nature', label: 'Nature', type: 'select', required: true, section: 'personalInfo' },
            { key: 'gender', label: 'Gender', type: 'select', required: false, section: 'personalInfo' },
            { key: 'dateOfBirth', label: 'Date of Birth', type: 'date', required: false, section: 'personalInfo' },
            { key: 'isMarried', label: 'Is Married', type: 'checkbox', required: false, section: 'personalInfo' },
            { key: 'dateOfMarriage', label: 'Date of Marriage', type: 'date', required: false, section: 'personalInfo' },
            { key: 'alternateNumbers', label: 'Alternate Numbers', type: 'text', required: false, section: 'contactInfo' },
            { key: 'primaryEmailId', label: 'Primary Email', type: 'email', required: false, section: 'contactInfo' },
            { key: 'alternateEmailIds', label: 'Alternate Emails', type: 'text', required: false, section: 'contactInfo' },
            { key: 'website', label: 'Website', type: 'url', required: false, section: 'contactInfo' },
            { key: 'workingState', label: 'Working State', type: 'text', required: false, section: 'locationInfo' },
            { key: 'domesticState', label: 'Domestic State', type: 'text', required: false, section: 'locationInfo' },
            { key: 'district', label: 'District', type: 'text', required: false, section: 'locationInfo' },
            { key: 'address', label: 'Address', type: 'textarea', required: false, section: 'locationInfo' },
            { key: 'workingArea', label: 'Working Area', type: 'text', required: false, section: 'locationInfo' },
            { key: 'hasAssociate', label: 'Has Associate', type: 'checkbox', required: false, section: 'associateInfo' },
            { key: 'associateName', label: 'Associate Name', type: 'text', required: false, section: 'associateInfo' },
            { key: 'associateRelation', label: 'Associate Relation', type: 'text', required: false, section: 'associateInfo' },
            { key: 'associateMobile', label: 'Associate Mobile', type: 'tel', required: false, section: 'associateInfo' },
            { key: 'usingWebsite', label: 'Using Website', type: 'checkbox', required: false, section: 'digitalPresence' },
            { key: 'websiteLink', label: 'Website Link', type: 'url', required: false, section: 'digitalPresence' },
            { key: 'usingCRMApp', label: 'Using CRM App', type: 'checkbox', required: false, section: 'digitalPresence' },
            { key: 'crmAppLink', label: 'CRM App Link', type: 'url', required: false, section: 'digitalPresence' },
            { key: 'transactionValue', label: 'Transaction Value', type: 'number', required: false, section: 'businessInfo' },
            { key: 'reraRegistrationNumber', label: 'RERA Registration Number', type: 'text', required: false, section: 'businessInfo' },
            { key: 'workingProfiles', label: 'Working Profiles', type: 'text', required: false, section: 'businessInfo' },
            { key: 'starRating', label: 'Star Rating', type: 'number', required: false, section: 'businessInfo' },
            { key: 'source', label: 'Source', type: 'text', required: false, section: 'businessInfo' },
            { key: 'remarks', label: 'Remarks', type: 'textarea', required: false, section: 'businessInfo' },
            { key: 'firmName', label: 'Firm Name', type: 'text', required: false, section: 'companyInfo' },
            { key: 'numberOfOffices', label: 'Number of Offices', type: 'number', required: false, section: 'companyInfo' },
            { key: 'numberOfBranches', label: 'Number of Branches', type: 'number', required: false, section: 'companyInfo' },
            { key: 'totalEmployeeStrength', label: 'Total Employee Strength', type: 'number', required: false, section: 'companyInfo' },
            { key: 'authorizedPersonName', label: 'Authorized Person Name', type: 'text', required: false, section: 'authorizedPerson' },
            { key: 'authorizedPersonEmail', label: 'Authorized Person Email', type: 'email', required: false, section: 'authorizedPerson' },
            { key: 'designation', label: 'Designation', type: 'text', required: false, section: 'authorizedPerson' },
            { key: 'marketingContact', label: 'Marketing Contact', type: 'text', required: false, section: 'marketingInfo' },
            { key: 'marketingDesignation', label: 'Marketing Designation', type: 'text', required: false, section: 'marketingInfo' },
            { key: 'placeOfPosting', label: 'Place of Posting', type: 'text', required: false, section: 'marketingInfo' },
            { key: 'department', label: 'Department', type: 'text', required: false, section: 'marketingInfo' }
        ];

        function createComprehensiveFormConfig(name, description, hierarchy) {
            return {
                name: name,
                description: description,
                fields: ALL_PERSON_FIELDS,
                sections: ['personalInfo', 'contactInfo', 'locationInfo', 'associateInfo', 'digitalPresence', 'businessInfo', 'companyInfo', 'authorizedPerson', 'marketingInfo'],
                settings: {
                    showSections: true,
                    allowConditionalFields: true,
                    validateOnChange: true,
                    autoSave: true
                },
                hierarchy: hierarchy,
                metadata: {
                    createdBy: 'Real Estate Forms Creation Script',
                    purpose: 'Comprehensive form for Real Estate division',
                    version: '1.0.0',
                    createdAt: new Date().toISOString()
                },
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                version: 1
            };
        }

        function saveFormConfig(type, id, config) {
            const key = `crm_form_config_${type}_${id}`;
            const formConfig = {
                id: `${type}_${id}`,
                type: type,
                associatedId: id,
                key: key,
                ...config
            };
            
            localStorage.setItem(key, JSON.stringify(formConfig));
            return formConfig;
        }

        function createForms() {
            showStatus('Creating forms...', 'info');
            showOutput('🏢 Starting Real Estate Forms Creation...\nCreating forms for Real Estate division\n\n');
            
            let output = '';
            let totalForms = 0;
            let successful = 0;
            
            REAL_ESTATE_STRUCTURE.categories.forEach(category => {
                output += `📁 Processing category: ${category.name}\n`;
                
                if (category.subcategories.length > 0) {
                    output += `  Found ${category.subcategories.length} subcategories\n`;
                    
                    category.subcategories.forEach(subcategory => {
                        try {
                            const formName = `${REAL_ESTATE_STRUCTURE.divisionName} - ${category.name} - ${subcategory.name}`;
                            const formDescription = `Comprehensive form for ${subcategory.name} in ${category.name} category of Real Estate division`;
                            
                            const hierarchy = {
                                divisionId: REAL_ESTATE_STRUCTURE.divisionId,
                                divisionName: REAL_ESTATE_STRUCTURE.divisionName,
                                categoryId: category.id,
                                categoryName: category.name,
                                subCategoryId: subcategory.id,
                                subCategoryName: subcategory.name
                            };
                            
                            const formConfig = createComprehensiveFormConfig(formName, formDescription, hierarchy);
                            const savedForm = saveFormConfig('subcategory', subcategory.id, formConfig);
                            
                            output += `✓ Created form for subcategory: ${category.name} - ${subcategory.name} (ID: ${subcategory.id})\n`;
                            successful++;
                            totalForms++;
                            
                        } catch (error) {
                            output += `✗ Failed to create form for subcategory: ${category.name} - ${subcategory.name}\n`;
                            totalForms++;
                        }
                    });
                } else {
                    try {
                        const formName = `${REAL_ESTATE_STRUCTURE.divisionName} - ${category.name}`;
                        const formDescription = `Comprehensive form for ${category.name} category of Real Estate division`;
                        
                        const hierarchy = {
                            divisionId: REAL_ESTATE_STRUCTURE.divisionId,
                            divisionName: REAL_ESTATE_STRUCTURE.divisionName,
                            categoryId: category.id,
                            categoryName: category.name,
                            subCategoryId: null,
                            subCategoryName: null
                        };
                        
                        const formConfig = createComprehensiveFormConfig(formName, formDescription, hierarchy);
                        const savedForm = saveFormConfig('category', category.id, formConfig);
                        
                        output += `  No subcategories found, creating category form\n`;
                        output += `✓ Created form for category: ${category.name} (ID: ${category.id})\n`;
                        successful++;
                        totalForms++;
                        
                    } catch (error) {
                        output += `✗ Failed to create form for category: ${category.name}\n`;
                        totalForms++;
                    }
                }
                output += '\n';
            });
            
            output += `📊 Summary:\n✓ Successfully created: ${successful} forms\n✗ Failed to create: ${totalForms - successful} forms\n\n🎉 Real Estate Forms Creation Complete!\n\nForms have been saved to browser localStorage and can now be accessed through the main CRM application.`;
            
            setTimeout(() => {
                appendOutput(output);
                showStatus(`Successfully created ${successful} out of ${totalForms} forms!`, 'success');
            }, 1000);
        }
        
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function showOutput(text) {
            const outputDiv = document.getElementById('output');
            outputDiv.style.display = 'block';
            outputDiv.textContent = text;
        }
        
        function appendOutput(text) {
            const outputDiv = document.getElementById('output');
            outputDiv.textContent += text;
        }
    </script>
</body>
</html>
