.real-estate-forms-creator {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
  color: #333;
  margin-bottom: 10px;
  font-size: 2.5rem;
}

.header p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

.structure-display {
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.structure-display h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.category-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.category-name {
  font-size: 1.1rem;
  margin-bottom: 10px;
  color: #333;
}

.subcategories {
  margin-left: 15px;
}

.subcategory-item {
  color: #666;
  margin: 5px 0;
  font-size: 0.95rem;
}

.actions {
  text-align: center;
  margin-bottom: 30px;
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.btn {
  border: none;
  padding: 12px 24px;
  margin: 0 10px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 160px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #218838;
  transform: translateY(-1px);
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
  transform: translateY(-1px);
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
  transform: translateY(-1px);
}

.status-message {
  padding: 15px;
  margin: 20px 0;
  border-radius: 6px;
  font-weight: 500;
  text-align: center;
}

.status-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-message.info {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.output-container {
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.output-container h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.output {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 20px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  max-height: 500px;
  overflow-y: auto;
  color: #333;
}

.output::-webkit-scrollbar {
  width: 8px;
}

.output::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.output::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.output::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive design */
@media (max-width: 768px) {
  .real-estate-forms-creator {
    padding: 10px;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .actions {
    padding: 20px 15px;
  }
  
  .btn {
    display: block;
    width: 100%;
    margin: 10px 0;
    min-width: auto;
  }
  
  .structure-display,
  .output-container {
    padding: 20px 15px;
  }
}
